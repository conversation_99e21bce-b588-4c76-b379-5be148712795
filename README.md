# 推箱子游戏 (Sokoban Game)

基于Python和Pygame开发的经典推箱子游戏，支持多关卡、角色动画和完整的游戏功能。

## 团队成员
- 熊秋锦：负责游戏主逻辑设计、地图数据结构实现及胜负判定模块开发
- 廖云川：负责Pygame窗口初始化、键盘事件处理、角色与地图渲染及界面信息显示
- 叶马可：负责关卡文件设计、移动合法性检测、步数记录功能及整体测试与调试

## 游戏特性
- 🎮 经典推箱子游戏玩法
- 🎨 精美的像素艺术素材
- 🏃 角色动作动画（支持四个方向）
- 📊 实时显示关卡、步数、进度信息
- 🎯 10个精心设计的关卡
- ⌨️ 支持键盘和WASD控制
- 🔄 关卡重置和切换功能
- 🖼️ 自定义素材支持
- 🌏 完美支持中文界面
- 📦 支持PyInstaller打包为exe文件

## 系统要求
- Python 3.6+
- Pygame 2.0+
- Windows/Linux/macOS

## 快速开始

### 1. 安装依赖
```bash
pip install pygame
```

### 2. 运行游戏
```bash
python main.py
```

### 3. 打包为exe（可选）
```bash
# 安装PyInstaller
pip install pyinstaller

# 运行打包脚本
python build.py
```
打包后的exe文件位于 `release/推箱子游戏.exe`

## 游戏控制
- **方向键** 或 **WASD**：移动角色
- **R键**：重置当前关卡
- **N键**：下一关
- **P键**：上一关
- **ESC键**：退出游戏

## 游戏规则
1. 控制角色移动，推动箱子到目标点
2. 当所有箱子都推到目标点时，关卡完成
3. 箱子在目标点上时显示为绿色
4. 不能推动多个箱子或将箱子推到墙上
5. 完成所有关卡即可通关

## 游戏元素
- 🧱 **墙壁**：不可通过的障碍物
- 🏃 **玩家**：可控制的角色，支持四方向动作
- 📦 **箱子**：需要推动的物体
- 🎯 **目标点**：箱子的目标位置
- ✅ **箱子在目标点**：正确放置的箱子

## 项目结构
```
Sokoban3/
├── main.py                      # 游戏主程序
├── sokoban_game.py              # 游戏核心类
├── build.py                     # 打包脚本
├── 推箱子游戏.spec               # PyInstaller配置文件
├── image/                       # 游戏素材文件夹
│   ├── wall.png                 # 墙壁素材 (32x32)
│   ├── box.png                  # 箱子素材 (32x32)
│   ├── boxfinished.png          # 完成状态箱子素材 (32x32)
│   ├── destination.png          # 目标点素材 (32x32)
│   └── character.png            # 角色动作表 (128x128, 4x4帧)
├── levels/                      # 关卡文件夹
│   ├── level_1.txt              # 关卡1
│   ├── level_2.txt              # 关卡2
│   └── ...                      # 关卡3-10
├── release/                     # 打包输出目录
│   └── 推箱子游戏.exe            # 打包后的可执行文件
├── 关卡设计指南.md               # 关卡设计文档
├── 游戏修复报告.md               # 修复记录文档
├── 基于Python的推箱子游戏开题思路.txt  # 项目设计文档
├── 遇到的难题.txt                # 开发问题记录
└── README.md                    # 项目说明文档
```

## 关卡设计
可以在`levels/`文件夹中添加新的关卡文件，使用以下字符设计关卡：

| 符号 | 含义 |
|------|------|
| `#` | 墙壁 |
| ` ` | 空地 |
| `$` | 箱子 |
| `.` | 目标点 |
| `@` | 玩家起始位置 |
| `*` | 箱子在目标点上 |
| `+` | 玩家在目标点上 |

详细的关卡设计指南请参考 `关卡设计指南.md`

## 技术特点
- **模块化设计**：地图数据、角色控制、渲染显示分离
- **数据结构**：二维列表存储地图，字符标识不同元素
- **图形界面**：Pygame实现30FPS流畅交互
- **素材系统**：支持PNG素材，自动缩放和方向检测
- **动作系统**：角色根据移动方向显示不同动作帧
- **可扩展性**：支持外部文件加载多关卡和自定义素材
- **中文支持**：自动检测系统中文字体，完美支持中文界面
- **错误处理**：完善的边界检查和错误恢复机制

## 素材规格
- **基础素材**：32x32像素PNG格式
- **角色素材**：128x128像素，包含4x4动作帧网格
  - 第1行：向下动作帧
  - 第2行：向左动作帧
  - 第3行：向右动作帧
  - 第4行：向上动作帧
- **显示尺寸**：所有素材自动缩放到40x40像素显示
- **备选方案**：如果素材加载失败，自动使用颜色块显示

## 故障排除

### 常见问题
如果游戏无法运行，请检查：
1. **Python版本**：确保使用Python 3.6+
2. **依赖安装**：确保已正确安装Pygame
   ```bash
   pip install pygame
   ```
3. **文件完整性**：确保`image/`和`levels/`文件夹存在且包含必要文件
4. **权限问题**：确保有读取游戏文件的权限

### 中文显示问题
游戏会自动检测系统中文字体：
- 优先使用微软雅黑 (microsoftyahei)
- 备选字体：黑体、宋体、楷体、仿宋
- 如果没有中文字体，会自动切换到英文界面

### 打包问题
如果打包失败，请检查：
1. 确保已安装PyInstaller：`pip install pyinstaller`
2. 确保`image/`和`levels/`文件夹存在
3. 检查控制台输出的错误信息

## 开发历程
本项目在开发过程中解决了多个技术难题：
- **中文字体支持**：解决了Pygame默认字体不支持中文的问题
- **按键冲突**：修复了关卡完成后按键无响应的问题
- **角色动画**：实现了128x128像素4x4动作帧的角色动画系统
- **边界检查**：完善了地图边界检查，防止数组越界错误
- **地图标准化**：确保所有关卡文件格式规整，避免加载错误

详细的开发记录请参考：
- `游戏修复报告.md` - 错误修复记录
- `遇到的难题.txt` - 开发问题记录
- `基于Python的推箱子游戏开题思路.txt` - 项目设计思路

## 许可证
本项目仅用于学习和教育目的。
