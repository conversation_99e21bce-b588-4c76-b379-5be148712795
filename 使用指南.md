# 推箱子游戏使用指南

## 🎮 快速开始

### 1. 启动游戏
运行 `python main.py` 后，你会看到美观的主界面菜单：

```
        推箱子游戏
      Sokoban Game

      > 开始游戏
        游戏设置
        退出游戏

使用方向键选择，回车确认，ESC退出
```

### 2. 主界面操作
- **方向键 ↑↓**：在菜单选项间移动
- **回车键 ⏎** 或 **空格键**：确认选择
- **ESC键**：退出游戏

### 3. 开始游戏
选择"开始游戏"后，直接进入第一关。游戏界面显示：
- 左上角：当前关卡信息
- 中间：游戏地图
- 底部：操作提示

## ⚙️ 游戏设置

在主界面选择"游戏设置"可以调整：

### 窗口大小设置
支持以下分辨率：
- 800 x 600 （适合小屏幕）
- 1024 x 768 （经典4:3比例）
- 1280 x 720 （高清16:9比例）
- 1366 x 768 （笔记本常用）
- 1920 x 1080 （全高清）

### 全屏模式
- 开启全屏：自动适应你的屏幕分辨率
- 关闭全屏：使用选定的窗口大小

## 🎯 游戏玩法

### 基本操作
- **方向键** 或 **WASD**：移动角色
- **R键**：重置当前关卡
- **N键**：下一关（需要完成当前关卡）
- **P键**：上一关
- **ESC键**：返回主界面

### 游戏目标
将所有橙色箱子推到黄色目标点上。当箱子正确放置时会变成绿色。

### 游戏规则
1. 箱子只能推，不能拉
2. 一次只能推一个箱子
3. 箱子不能推到墙壁或其他箱子上
4. 完成当前关卡后才能进入下一关

## 🏆 完成游戏

### 关卡完成
完成关卡后会显示：
```
关卡 X 完成！
按 N 键进入下一关，按 ESC 键返回主界面
```

### 游戏通关
完成所有10个关卡后：
```
恭喜！所有关卡完成！
按 ESC 键返回主界面
```

游戏会自动返回主界面，你可以：
- 重新开始游戏
- 调整设置
- 退出游戏

## 💡 游戏技巧

1. **仔细观察**：在移动前先观察地图布局
2. **逆向思考**：从目标位置反推箱子的移动路径
3. **避免死角**：不要把箱子推到无法移动的角落
4. **善用重置**：遇到困难时按R键重新开始
5. **循序渐进**：从简单关卡开始，逐步提高难度

## 🔧 故障排除

### 游戏无法启动
1. 确保已安装Python 3.6+
2. 安装Pygame：`pip install pygame`
3. 检查文件完整性

### 中文显示异常
游戏会自动检测中文字体，如果显示异常：
1. 确保系统安装了中文字体
2. 游戏会自动降级到英文界面

### 窗口大小问题
如果窗口太大或太小：
1. 在主界面进入"游戏设置"
2. 调整到合适的窗口大小
3. 或者尝试全屏模式

## 📞 获取帮助

如果遇到其他问题，请检查：
- `README.md` - 完整的项目说明
- `遇到的难题.txt` - 常见问题解决方案
- `游戏修复报告.md` - 已知问题和修复记录

祝你游戏愉快！🎉
