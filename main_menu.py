#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推箱子游戏主界面菜单
包含开始游戏、设置、退出等功能
"""

import pygame

# 颜色定义
COLORS = {
    'WHITE': (255, 255, 255),
    'BLACK': (0, 0, 0),
    'GRAY': (128, 128, 128),
    'LIGHT_GRAY': (200, 200, 200),
    'DARK_GRAY': (64, 64, 64),
    'BLUE': (0, 100, 200),
    'LIGHT_BLUE': (100, 150, 255),
    'GREEN': (0, 150, 0),
    'RED': (200, 0, 0)
}

class MainMenu:
    """游戏主界面菜单类"""
    
    def __init__(self, screen):
        """初始化主界面"""
        self.screen = screen
        self.screen_width = screen.get_width()
        self.screen_height = screen.get_height()
        
        # 菜单状态
        self.current_menu = "main"  # main, settings
        self.selected_option = 0
        self.menu_options = []
        
        # 字体初始化
        self.init_fonts()
        
        # 设置选项
        self.window_sizes = [
            (800, 600),
            (1024, 768),
            (1280, 720),
            (1366, 768),
            (1920, 1080)
        ]
        self.current_window_size_index = 2  # 默认1280x720
        self.is_fullscreen = False
        
        # 初始化菜单选项
        self.init_menu_options()
        
    def init_fonts(self):
        """初始化字体"""
        pygame.font.init()
        
        # 常见的Windows中文字体列表
        chinese_fonts = [
            'microsoftyahei',  # 微软雅黑
            'simhei',          # 黑体
            'simsun',          # 宋体
            'kaiti',           # 楷体
            'fangsong',        # 仿宋
            'arial unicode ms' # Arial Unicode MS
        ]
        
        self.title_font = None
        self.menu_font = None
        self.small_font = None
        
        # 尝试加载中文字体
        for font_name in chinese_fonts:
            try:
                test_font = pygame.font.SysFont(font_name, 48)
                # 测试是否能正确渲染中文
                test_surface = test_font.render('测试', True, (0, 0, 0))
                if test_surface.get_width() > 0:
                    self.title_font = test_font
                    self.menu_font = pygame.font.SysFont(font_name, 32)
                    self.small_font = pygame.font.SysFont(font_name, 24)
                    self.use_chinese = True
                    break
            except:
                continue
        
        # 如果没有找到合适的中文字体，使用默认字体
        if self.title_font is None:
            self.title_font = pygame.font.Font(None, 64)
            self.menu_font = pygame.font.Font(None, 48)
            self.small_font = pygame.font.Font(None, 32)
            self.use_chinese = False
            
    def init_menu_options(self):
        """初始化菜单选项"""
        if self.current_menu == "main":
            if self.use_chinese:
                self.menu_options = [
                    "开始游戏",
                    "游戏设置", 
                    "退出游戏"
                ]
            else:
                self.menu_options = [
                    "Start Game",
                    "Settings",
                    "Exit Game"
                ]
        elif self.current_menu == "settings":
            if self.use_chinese:
                if self.is_fullscreen:
                    window_size_text = "窗口大小: (全屏模式下不可用)"
                else:
                    window_size_text = f"窗口大小: {self.window_sizes[self.current_window_size_index][0]}x{self.window_sizes[self.current_window_size_index][1]}"
                fullscreen_text = f"全屏模式: {'开启' if self.is_fullscreen else '关闭'}"
                self.menu_options = [
                    window_size_text,
                    fullscreen_text,
                    "返回主菜单"
                ]
            else:
                if self.is_fullscreen:
                    window_size_text = "Window Size: (Unavailable in fullscreen)"
                else:
                    window_size_text = f"Window Size: {self.window_sizes[self.current_window_size_index][0]}x{self.window_sizes[self.current_window_size_index][1]}"
                fullscreen_text = f"Fullscreen: {'On' if self.is_fullscreen else 'Off'}"
                self.menu_options = [
                    window_size_text,
                    fullscreen_text,
                    "Back to Main"
                ]
                
        # 确保选中的选项在有效范围内
        if self.selected_option >= len(self.menu_options):
            self.selected_option = 0
            
    def handle_input(self, event):
        """处理输入事件"""
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_UP:
                self.selected_option = (self.selected_option - 1) % len(self.menu_options)
            elif event.key == pygame.K_DOWN:
                self.selected_option = (self.selected_option + 1) % len(self.menu_options)
            elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
                return self.select_option()
            elif event.key == pygame.K_ESCAPE:
                if self.current_menu == "settings":
                    self.current_menu = "main"
                    self.selected_option = 0
                    self.init_menu_options()
                else:
                    return "exit"
        return None

    def get_current_window_size(self):
        """获取当前窗口大小"""
        return self.window_sizes[self.current_window_size_index]

    def render(self):
        """渲染主界面"""
        # 清空屏幕
        self.screen.fill(COLORS['WHITE'])

        # 绘制背景渐变效果
        self.draw_background()

        # 绘制标题
        self.draw_title()

        # 绘制菜单选项
        self.draw_menu_options()

        # 绘制底部信息
        self.draw_footer()

    def draw_background(self):
        """绘制背景"""
        # 简单的渐变背景效果
        for y in range(self.screen_height):
            color_ratio = y / self.screen_height
            r = int(240 + (255 - 240) * color_ratio)
            g = int(248 + (255 - 248) * color_ratio)
            b = int(255)
            color = (r, g, b)
            pygame.draw.line(self.screen, color, (0, y), (self.screen_width, y))

    def draw_title(self):
        """绘制标题"""
        if self.use_chinese:
            title_text = "推箱子游戏"
            subtitle_text = "Sokoban Game"
        else:
            title_text = "Sokoban Game"
            subtitle_text = "Push the boxes to targets"

        # 主标题
        title_surface = self.title_font.render(title_text, True, COLORS['DARK_GRAY'])
        title_rect = title_surface.get_rect(center=(self.screen_width // 2, self.screen_height // 4))
        self.screen.blit(title_surface, title_rect)

        # 副标题
        subtitle_surface = self.small_font.render(subtitle_text, True, COLORS['GRAY'])
        subtitle_rect = subtitle_surface.get_rect(center=(self.screen_width // 2, self.screen_height // 4 + 60))
        self.screen.blit(subtitle_surface, subtitle_rect)

    def draw_menu_options(self):
        """绘制菜单选项"""
        start_y = self.screen_height // 2
        option_height = 60

        for i, option in enumerate(self.menu_options):
            y_pos = start_y + i * option_height

            # 检查选项是否可用（在设置菜单中，全屏模式下窗口大小不可用）
            is_disabled = (self.current_menu == "settings" and i == 0 and self.is_fullscreen)

            # 绘制选项背景
            if i == self.selected_option and not is_disabled:
                # 选中状态
                bg_color = COLORS['LIGHT_BLUE']
                text_color = COLORS['WHITE']
                # 绘制选中背景
                option_rect = pygame.Rect(self.screen_width // 2 - 150, y_pos - 20, 300, 40)
                pygame.draw.rect(self.screen, bg_color, option_rect, border_radius=10)
                pygame.draw.rect(self.screen, COLORS['BLUE'], option_rect, 2, border_radius=10)
            else:
                # 未选中状态或禁用状态
                if is_disabled:
                    text_color = COLORS['LIGHT_GRAY']  # 禁用选项显示为浅灰色
                else:
                    text_color = COLORS['DARK_GRAY']

            # 绘制选项文本
            option_surface = self.menu_font.render(option, True, text_color)
            option_rect = option_surface.get_rect(center=(self.screen_width // 2, y_pos))
            self.screen.blit(option_surface, option_rect)

    def draw_footer(self):
        """绘制底部信息"""
        if self.use_chinese:
            if self.current_menu == "main":
                footer_text = "使用方向键选择，回车确认，ESC退出"
            else:
                footer_text = "使用方向键选择，回车确认，ESC返回"
        else:
            if self.current_menu == "main":
                footer_text = "Use arrow keys to select, Enter to confirm, ESC to exit"
            else:
                footer_text = "Use arrow keys to select, Enter to confirm, ESC to go back"

        footer_surface = self.small_font.render(footer_text, True, COLORS['GRAY'])
        footer_rect = footer_surface.get_rect(center=(self.screen_width // 2, self.screen_height - 50))
        self.screen.blit(footer_surface, footer_rect)
        
    def select_option(self):
        """选择菜单选项"""
        if self.current_menu == "main":
            if self.selected_option == 0:  # 开始游戏
                return "start_game"
            elif self.selected_option == 1:  # 设置
                self.current_menu = "settings"
                self.selected_option = 0
                self.init_menu_options()
            elif self.selected_option == 2:  # 退出
                return "exit"
        elif self.current_menu == "settings":
            if self.selected_option == 0:  # 窗口大小
                # 只有在非全屏模式下才能更改窗口大小
                if not self.is_fullscreen:
                    self.current_window_size_index = (self.current_window_size_index + 1) % len(self.window_sizes)
                    self.init_menu_options()
                    return "change_window_size"
            elif self.selected_option == 1:  # 全屏
                # 先切换全屏状态，然后返回动作
                self.is_fullscreen = not self.is_fullscreen
                self.init_menu_options()
                return "toggle_fullscreen"
            elif self.selected_option == 2:  # 返回
                self.current_menu = "main"
                self.selected_option = 0
                self.init_menu_options()
        return None
