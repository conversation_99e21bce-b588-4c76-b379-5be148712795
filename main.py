#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推箱子游戏主程序
作者：熊秋锦、廖云川、叶马可
""" 

import pygame
from sokoban_game import <PERSON><PERSON><PERSON><PERSON><PERSON>
from main_menu import MainMenu

# 游戏配置
WINDOW_WIDTH = 1080
WINDOW_HEIGHT = 900
FPS = 30
TITLE = "推箱子游戏 - Sokoban"

def main():
    """游戏主函数"""

    try:
        # 初始化Pygame
        pygame.init()

        # 创建游戏窗口
        screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption(TITLE)

        # 创建时钟对象控制帧率
        clock = pygame.time.Clock()

        # 游戏状态管理
        game_state = "menu"  # menu, playing, settings
        main_menu = MainMenu(screen)
        game = None

        # 游戏主循环
        running = True

        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif game_state == "menu":
                    # 主界面事件处理
                    menu_action = main_menu.handle_input(event)
                    if menu_action == "start_game":
                        # 开始游戏
                        try:
                            game = SokobanGame(screen)
                            game_state = "playing"
                        except Exception as e:
                            print(f"游戏初始化失败: {e}")
                            running = False
                    elif menu_action == "change_window_size":
                        # 改变窗口大小（仅在非全屏模式下）
                        if not main_menu.is_fullscreen:
                            new_size = main_menu.get_current_window_size()
                            screen = pygame.display.set_mode(new_size)
                            main_menu.screen = screen
                            main_menu.screen_width = screen.get_width()
                            main_menu.screen_height = screen.get_height()
                            # 如果游戏正在运行，也需要更新游戏的屏幕引用
                            if game:
                                game.screen = screen
                                game.screen_width = screen.get_width()
                                game.screen_height = screen.get_height()
                    elif menu_action == "toggle_fullscreen":
                        # 切换全屏
                        if main_menu.is_fullscreen:
                            # 切换到全屏模式
                            screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
                        else:
                            # 切换到窗口模式
                            size = main_menu.get_current_window_size()
                            screen = pygame.display.set_mode(size)
                        main_menu.screen = screen
                        main_menu.screen_width = screen.get_width()
                        main_menu.screen_height = screen.get_height()
                        # 如果游戏正在运行，也需要更新游戏的屏幕引用
                        if game:
                            game.screen = screen
                            game.screen_width = screen.get_width()
                            game.screen_height = screen.get_height()
                    elif menu_action == "exit":
                        running = False
                elif game_state == "playing":
                    # 游戏中事件处理
                    if event.type == pygame.KEYDOWN:
                        if event.key == pygame.K_ESCAPE:
                            # ESC键返回主界面
                            game_state = "menu"
                            game = None
                        elif event.key == pygame.K_r:
                            # R键重置当前关卡
                            try:
                                game.reset_level()
                            except Exception as e:
                                print(f"重置关卡失败: {e}")
                                game_state = "menu"
                                game = None
                        elif event.key == pygame.K_n:
                            # N键下一关
                            try:
                                if game.level_completed:  # 只有在关卡完成时才能进入下一关
                                    if game.next_level():
                                        print(f"进入关卡 {game.current_level}")
                                    else:
                                        print("恭喜！已完成所有关卡，返回主界面")
                                        game_state = "menu"
                                        game = None
                            except Exception as e:
                                print(f"切换到下一关失败: {e}")
                                game_state = "menu"
                                game = None
                        elif event.key == pygame.K_p:
                            # P键上一关
                            try:
                                if game.previous_level():
                                    print(f"返回关卡 {game.current_level}")
                                else:
                                    print("已经是第一关")
                            except Exception as e:
                                print(f"切换到上一关失败: {e}")
                                game_state = "menu"
                                game = None
                        elif event.key in [pygame.K_UP, pygame.K_DOWN, pygame.K_LEFT, pygame.K_RIGHT,
                                         pygame.K_w, pygame.K_s, pygame.K_a, pygame.K_d]:
                            # 只有方向键才传递给handle_input
                            try:
                                game.handle_input(event.key)
                            except Exception as e:
                                print(f"处理玩家输入失败: {e}")
                                game_state = "menu"
                                game = None

            # 根据游戏状态进行更新和渲染
            if game_state == "menu":
                # 渲染主界面
                main_menu.render()
            elif game_state == "playing" and game:
                # 更新游戏状态
                try:
                    game.update()
                    # 检查是否完成所有关卡
                    if game.game_completed:
                        print("恭喜！完成所有关卡，返回主界面")
                        game_state = "menu"
                        game = None
                except Exception as e:
                    print(f"游戏状态更新失败: {e}")
                    game_state = "menu"
                    game = None

                # 渲染游戏画面
                if game:
                    try:
                        game.render()
                    except Exception as e:
                        print(f"游戏渲染失败: {e}")
                        game_state = "menu"
                        game = None

            # 更新显示
            pygame.display.flip()

            # 控制帧率
            clock.tick(FPS)

    except Exception as e:
        print(f"游戏发生致命错误: {e}")

    finally:
        # 清理资源
        pygame.quit()

if __name__ == "__main__":
    main()
