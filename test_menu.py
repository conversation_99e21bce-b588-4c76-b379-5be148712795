#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主界面菜单功能
"""

import pygame
import sys
from main_menu import MainMenu

def test_menu():
    """测试主界面菜单"""
    try:
        # 初始化Pygame
        pygame.init()
        
        # 创建测试窗口
        screen = pygame.display.set_mode((800, 600))
        pygame.display.set_caption("测试主界面")
        
        # 创建主界面
        menu = MainMenu(screen)
        
        # 创建时钟
        clock = pygame.time.Clock()
        
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                else:
                    action = menu.handle_input(event)
                    if action:
                        print(f"菜单动作: {action}")
                        if action == "exit":
                            running = False
                        elif action == "start_game":
                            print("开始游戏被选择")
                        elif action == "change_window_size":
                            print(f"窗口大小改变为: {menu.get_current_window_size()}")
                        elif action == "toggle_fullscreen":
                            print(f"全屏模式: {menu.is_fullscreen}")
            
            # 渲染菜单
            menu.render()
            pygame.display.flip()
            clock.tick(60)
            
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        pygame.quit()

if __name__ == "__main__":
    test_menu()
