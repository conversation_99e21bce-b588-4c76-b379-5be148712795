# 推箱子游戏 v2.0 更新说明

## 🎉 主要新功能

### 1. 全新主界面菜单系统
- **美观的主菜单界面**：渐变背景，现代化设计
- **三个主要选项**：开始游戏、游戏设置、退出游戏
- **直观的操作方式**：方向键选择，回车确认
- **中英文双语支持**：自动检测系统字体

### 2. 完整的窗口设置功能
- **多种窗口大小**：
  - 800x600 (适合小屏幕)
  - 1024x768 (经典4:3)
  - 1280x720 (高清16:9)
  - 1366x768 (笔记本常用)
  - 1920x1080 (全高清)
- **全屏模式支持**：自动适应屏幕分辨率
- **实时切换**：无需重启游戏即可调整

### 3. 改进的游戏流程
- **智能状态管理**：主界面和游戏界面无缝切换
- **ESC键新功能**：返回主界面而不是直接退出
- **完整游戏循环**：完成所有关卡后自动返回主界面
- **更好的提示信息**：清晰的操作指导

## 🔧 技术改进

### 新增文件
- `main_menu.py` - 主界面菜单系统
- `test_menu.py` - 菜单功能测试脚本
- `使用指南.md` - 详细的使用说明
- `更新说明.md` - 本文档

### 代码重构
- **main.py**：重构为状态机模式，支持多界面切换
- **sokoban_game.py**：改进游戏完成检测和提示信息
- **README.md**：更新项目说明和功能介绍

## 🎮 使用方法

### 启动游戏
```bash
python main.py
```

### 主界面操作
1. **开始游戏**：进入第一关开始游戏
2. **游戏设置**：调整窗口大小和全屏模式
3. **退出游戏**：安全退出程序

### 游戏中操作
- **方向键/WASD**：移动角色
- **R键**：重置关卡
- **N键**：下一关（需要完成当前关卡）
- **P键**：上一关
- **ESC键**：返回主界面

## 🏆 游戏流程

1. **启动** → 主界面菜单
2. **开始游戏** → 第一关
3. **完成关卡** → 可选择下一关或返回主界面
4. **完成所有关卡** → 自动返回主界面
5. **随时按ESC** → 返回主界面

## 🛠️ 兼容性

### 系统要求
- Python 3.6+
- Pygame 2.0+
- Windows/Linux/macOS

### 字体支持
- 自动检测中文字体（微软雅黑、黑体、宋体等）
- 如无中文字体，自动切换到英文界面
- 完美支持中英文混合显示

### 分辨率支持
- 支持多种常见分辨率
- 全屏模式自动适应屏幕
- 游戏界面自动居中显示

## 🐛 已修复问题

1. **ESC键行为**：现在返回主界面而不是直接退出
2. **游戏完成检测**：正确检测所有关卡完成状态
3. **窗口切换**：修复全屏和窗口模式切换问题
4. **状态管理**：解决界面切换时的状态混乱问题

## 🔮 未来计划

- 添加音效和背景音乐
- 增加更多关卡
- 添加关卡编辑器
- 支持自定义主题
- 添加成就系统

## 📝 开发团队

- **熊秋锦**：游戏逻辑和地图系统
- **廖云川**：界面渲染和事件处理
- **叶马可**：关卡设计和测试调试

感谢团队成员的辛勤工作，让这个项目更加完善！

---

**版本**：v2.0  
**发布日期**：2026年1月  
**兼容性**：向下兼容v1.0的所有功能
